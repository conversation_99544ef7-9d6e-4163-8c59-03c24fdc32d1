#!/usr/bin/env python3
import requests
import json

def test_upload_api():
    url = "http://localhost:8000/api/v1/documents/upload"
    
    # 准备文件和数据
    files = {
        'file': ('test_upload2.txt', '这是第二个测试文档，用于验证API修复。\n\n包含一些关于机器学习和深度学习的内容。', 'text/plain')
    }
    
    data = {
        'kb_id': 'test-kb-002',
        'auto_process': 'true',
        'uploaded_by': 'api_test_user'
    }
    
    try:
        response = requests.post(url, files=files, data=data, timeout=60)
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            print("✅ 文档上传成功！")
            result = response.json()
            print(f"文档ID: {result.get('doc_id', 'N/A')}")
            print(f"文件名: {result.get('filename', 'N/A')}")
            print(f"消息: {result.get('message', 'N/A')}")
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_upload_api()
