#!/usr/bin/env python3
import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, '/home/<USER>/llama-stady/mixrag')

from src.rag.infrastructure.db.document_operations import document_operations

async def debug_api_response():
    """调试API响应问题"""
    print("🔍 调试API响应问题...")
    
    try:
        # 测试 list_documents 方法
        print("\n📋 测试 list_documents 方法...")

        # 不带过滤条件
        result = await document_operations.list_documents(
            page=1,
            page_size=10,
            filters={}
        )
        
        print(f"📊 查询结果: {result['total']} 条记录")
        
        for i, doc in enumerate(result['records'], 1):
            print(f"\n记录 {i}:")
            print(f"  doc_id: {doc.get('doc_id')}")
            print(f"  original_filename: {doc.get('original_filename')}")
            print(f"  kb_id: {doc.get('kb_id')}")
            print(f"  upload_status: {doc.get('upload_status')}")
            print(f"  process_status: {doc.get('process_status')}")
            
            # 检查所有字段
            print(f"  所有字段: {list(doc.keys())}")
        
        # 带 kb_id 过滤条件
        print(f"\n🔍 测试带 kb_id 过滤条件...")
        result_filtered = await document_operations.list_documents(
            page=1,
            page_size=10,
            filters={'kb_id': 'test-kb-new-001'}
        )
        
        print(f"📊 过滤后结果: {result_filtered['total']} 条记录")
        
        for i, doc in enumerate(result_filtered['records'], 1):
            print(f"\n过滤记录 {i}:")
            print(f"  doc_id: {doc.get('doc_id')}")
            print(f"  original_filename: {doc.get('original_filename')}")
            print(f"  kb_id: {doc.get('kb_id')}")
        

        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_api_response())
