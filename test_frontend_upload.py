#!/usr/bin/env python3
import requests
import json

def test_frontend_upload():
    """测试前端上传API（模拟前端调用）"""
    url = "http://localhost:8000/api/v1/documents/upload"
    
    # 模拟前端传递的数据
    files = {
        'file': ('frontend_test.txt', '这是前端测试文档。\n\n包含一些测试内容，用于验证前端上传功能。', 'text/plain')
    }
    
    data = {
        'kb_id': 'frontend-test-kb',  # 前端传递的知识库ID
        'auto_process': 'true',
        'uploaded_by': 'frontend_user'
    }
    
    try:
        print("🚀 开始测试前端上传...")
        response = requests.post(url, files=files, data=data, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 前端上传测试成功！")
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 提取文档ID
            if result.get('success') and result.get('data'):
                doc_id = result['data'].get('doc_id')
                if doc_id:
                    print(f"📄 文档ID: {doc_id}")
                    return doc_id
        else:
            print(f"❌ 前端上传测试失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return None

def test_document_list():
    """测试文档列表API"""
    url = "http://localhost:8000/api/v1/documents/list"
    
    params = {
        'page': 1,
        'page_size': 10,
        'kb_id': 'frontend-test-kb'  # 过滤特定知识库的文档
    }
    
    try:
        print("\n📋 测试文档列表...")
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            print("✅ 文档列表获取成功！")
            result = response.json()
            
            if result.get('success') and result.get('data'):
                documents = result['data'].get('records', [])
                print(f"📊 找到 {len(documents)} 个文档:")
                
                for doc in documents:
                    print(f"  - {doc.get('original_filename')} (KB: {doc.get('kb_id')})")
            else:
                print("📝 暂无文档")
        else:
            print(f"❌ 文档列表获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    # 测试上传
    doc_id = test_frontend_upload()
    
    # 等待一下让文档处理
    if doc_id:
        import time
        print("\n⏳ 等待文档处理...")
        time.sleep(3)
        
        # 测试文档列表
        test_document_list()
