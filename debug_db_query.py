#!/usr/bin/env python3
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def debug_db_query():
    # 加载环境变量
    load_dotenv()
    
    # 数据库连接参数
    db_config = {
        'host': os.getenv('POSTGRES_HOST', '*************'),
        'port': int(os.getenv('POSTGRES_PORT', 5433)),
        'database': os.getenv('POSTGRES_DB', 'pgsql'),
        'user': os.getenv('POSTGRES_USER', 'root'),
        'password': os.getenv('POSTGRES_PASSWORD', 'root123')
    }
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(**db_config)
        
        # 模拟 list_documents 的查询
        print("🔍 测试文档列表查询...")
        
        # 构建查询条件
        where_conditions = ["1=1"]  # 基础条件
        params = []
        param_count = 0
        
        # 添加 kb_id 过滤条件
        kb_id = "test-kb-new-001"
        if kb_id:
            param_count += 1
            where_conditions.append(f"kb_id = ${param_count}")
            params.append(kb_id)
        
        where_clause = " AND ".join(where_conditions)
        order_clause = "ORDER BY created_at DESC"
        
        # 构建查询语句
        list_query = f"""
        SELECT doc_id, original_filename, file_extension, file_size, mime_type,
               created_at, updated_at, upload_status, process_status, uploaded_by,
               chunks_count, entities_count, relationships_count, minio_bucket, minio_object_key,
               content_hash, error_message, kb_id
        FROM mixrag_documents
        WHERE {where_clause}
        {order_clause}
        LIMIT ${param_count + 1} OFFSET ${param_count + 2}
        """
        
        # 添加分页参数
        params.extend([10, 0])  # limit=10, offset=0
        
        print(f"📋 执行查询:")
        print(f"SQL: {list_query}")
        print(f"参数: {params}")
        
        # 执行查询
        rows = await conn.fetch(list_query, *params)
        
        print(f"\n📊 查询结果: {len(rows)} 条记录")
        
        for i, row in enumerate(rows, 1):
            print(f"\n记录 {i}:")
            print(f"  doc_id: {row['doc_id']}")
            print(f"  original_filename: {row['original_filename']}")
            print(f"  kb_id: {row['kb_id']}")
            print(f"  upload_status: {row['upload_status']}")
            print(f"  process_status: {row['process_status']}")
            
            # 转换为字典（模拟代码中的操作）
            doc_dict = {
                "doc_id": row["doc_id"],
                "original_filename": row["original_filename"],
                "file_extension": row["file_extension"],
                "file_size": row["file_size"],
                "mime_type": row["mime_type"],
                "created_at": row["created_at"],
                "updated_at": row["updated_at"],
                "upload_status": row["upload_status"],
                "process_status": row["process_status"],
                "uploaded_by": row["uploaded_by"],
                "chunks_count": row["chunks_count"],
                "entities_count": row["entities_count"],
                "relationships_count": row["relationships_count"],
                "minio_bucket": row["minio_bucket"],
                "minio_object_key": row["minio_object_key"],
                "content_hash": row["content_hash"],
                "error_message": row["error_message"],
                "kb_id": row["kb_id"],
            }
            
            print(f"  转换后的字典 kb_id: {doc_dict['kb_id']}")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_db_query())
