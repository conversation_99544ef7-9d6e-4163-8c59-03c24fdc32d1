#!/usr/bin/env python3
import requests
import json

def test_document_list_api():
    """测试文档列表API"""
    url = "http://localhost:8000/api/v1/documents/list"
    
    # 测试不带过滤条件
    print("🔍 测试文档列表API（不带过滤条件）...")
    try:
        response = requests.get(url, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功！")
            print(f"响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and result.get('data'):
                documents = result['data'].get('records', [])
                print(f"\n📊 找到 {len(documents)} 个文档:")
                
                for i, doc in enumerate(documents, 1):
                    print(f"  {i}. 文件名: {doc.get('original_filename')}")
                    print(f"     文档ID: {doc.get('doc_id')}")
                    print(f"     知识库ID: {doc.get('kb_id')}")
                    print(f"     上传状态: {doc.get('upload_status')}")
                    print(f"     处理状态: {doc.get('process_status')}")
                    print()
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试带 kb_id 过滤条件
    print("\n🔍 测试文档列表API（带kb_id过滤）...")
    params = {
        'kb_id': 'frontend-test-kb',
        'page': 1,
        'page_size': 10
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 过滤API调用成功！")
            
            if result.get('success') and result.get('data'):
                documents = result['data'].get('records', [])
                print(f"📊 过滤后找到 {len(documents)} 个文档:")
                
                for i, doc in enumerate(documents, 1):
                    print(f"  {i}. 文件名: {doc.get('original_filename')}")
                    print(f"     知识库ID: {doc.get('kb_id')}")
        else:
            print(f"❌ 过滤API调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 过滤请求失败: {e}")

if __name__ == "__main__":
    test_document_list_api()
