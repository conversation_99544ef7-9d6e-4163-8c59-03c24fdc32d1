#!/usr/bin/env python3
import requests
import json
import time

def test_final_upload():
    """最终测试文档上传和列表API"""
    url = "http://localhost:8000/api/v1/documents/upload"
    
    # 创建一个全新的测试文档
    content = f"""最终测试文档 - {time.time()}

这是一个用于最终测试的文档，用于验证 kb_id 功能是否正常工作。

测试内容包括：
1. 文档上传功能
2. kb_id 字段的正确保存
3. 文档列表API的正确返回
4. kb_id 过滤功能

这个测试将验证整个修复是否成功。"""
    
    files = {
        'file': (f'final_test_{int(time.time())}.txt', content, 'text/plain')
    }
    
    data = {
        'kb_id': 'final-test-kb-123',  # 全新的知识库ID
        'auto_process': 'false',  # 不自动处理，避免干扰
        'uploaded_by': 'final_test_user'
    }
    
    try:
        print("🚀 开始最终测试文档上传...")
        response = requests.post(url, files=files, data=data, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 文档上传成功！")
            result = response.json()
            print(f"上传响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and result.get('data'):
                doc_id = result['data'].get('doc_id')
                if doc_id:
                    print(f"📄 新文档ID: {doc_id}")
                    
                    # 等待一下
                    print("\n⏳ 等待 3 秒...")
                    time.sleep(3)
                    
                    # 测试文档列表API
                    test_document_list_api(doc_id)
                    
                    return doc_id
        else:
            print(f"❌ 文档上传失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return None

def test_document_list_api(expected_doc_id=None):
    """测试文档列表API"""
    url = "http://localhost:8000/api/v1/documents/list"
    
    # 测试不带过滤条件
    print("\n📋 测试文档列表API（不带过滤条件）...")
    try:
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 文档列表获取成功！")
            
            if result.get('success') and result.get('data'):
                documents = result['data'].get('records', [])
                print(f"📊 找到 {len(documents)} 个文档")
                
                # 查找我们刚上传的文档
                found_doc = None
                for doc in documents:
                    if expected_doc_id and doc.get('doc_id') == expected_doc_id:
                        found_doc = doc
                        break
                
                if found_doc:
                    print(f"\n🎯 找到刚上传的文档:")
                    print(f"  文档ID: {found_doc.get('doc_id')}")
                    print(f"  文件名: {found_doc.get('original_filename')}")
                    print(f"  知识库ID: {found_doc.get('kb_id')}")
                    print(f"  上传状态: {found_doc.get('upload_status')}")
                    print(f"  处理状态: {found_doc.get('process_status')}")
                    
                    if found_doc.get('kb_id') == 'final-test-kb-123':
                        print("✅ kb_id 字段正确！")
                    else:
                        print(f"❌ kb_id 字段错误: 期望 'final-test-kb-123', 实际 '{found_doc.get('kb_id')}'")
                else:
                    print("❌ 未找到刚上传的文档")
                    
        else:
            print(f"❌ 文档列表获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试带 kb_id 过滤条件
    print(f"\n🔍 测试文档列表API（带kb_id过滤）...")
    params = {
        'kb_id': 'final-test-kb-123',
        'page': 1,
        'page_size': 10
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 过滤API调用成功！")
            
            if result.get('success') and result.get('data'):
                documents = result['data'].get('records', [])
                print(f"📊 过滤后找到 {len(documents)} 个文档")
                
                if len(documents) > 0:
                    for i, doc in enumerate(documents, 1):
                        print(f"  {i}. 文件名: {doc.get('original_filename')}")
                        print(f"     知识库ID: {doc.get('kb_id')}")
                        
                        if doc.get('kb_id') == 'final-test-kb-123':
                            print("     ✅ 过滤功能正常！")
                        else:
                            print(f"     ❌ 过滤功能异常: {doc.get('kb_id')}")
                else:
                    print("❌ 过滤后没有找到文档")
            else:
                print("📝 过滤后暂无文档")
        else:
            print(f"❌ 过滤API调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 过滤请求失败: {e}")

if __name__ == "__main__":
    test_final_upload()
