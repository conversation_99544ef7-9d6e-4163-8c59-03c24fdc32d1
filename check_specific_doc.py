#!/usr/bin/env python3
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def check_specific_document():
    # 加载环境变量
    load_dotenv()
    
    # 数据库连接参数
    db_config = {
        'host': os.getenv('POSTGRES_HOST', '*************'),
        'port': int(os.getenv('POSTGRES_PORT', 5433)),
        'database': os.getenv('POSTGRES_DB', 'pgsql'),
        'user': os.getenv('POSTGRES_USER', 'root'),
        'password': os.getenv('POSTGRES_PASSWORD', 'root123')
    }
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(**db_config)
        
        # 查询特定文档
        doc_id = "doc_20250721_125620_8d10e415"
        query = """
        SELECT doc_id, original_filename, kb_id, upload_status, process_status, 
               created_at, uploaded_by, file_size
        FROM mixrag_documents 
        WHERE doc_id = $1
        """
        
        row = await conn.fetchrow(query, doc_id)
        
        if row:
            print(f"📊 文档详情:")
            print(f"文档ID: {row['doc_id']}")
            print(f"文件名: {row['original_filename']}")
            print(f"知识库ID: {row['kb_id']}")
            print(f"上传状态: {row['upload_status']}")
            print(f"处理状态: {row['process_status']}")
            print(f"创建时间: {row['created_at']}")
            print(f"上传者: {row['uploaded_by']}")
            print(f"文件大小: {row['file_size']} bytes")
        else:
            print(f"❌ 未找到文档: {doc_id}")
        
        # 查询所有字段
        query_all = "SELECT * FROM mixrag_documents WHERE doc_id = $1"
        row_all = await conn.fetchrow(query_all, doc_id)
        
        if row_all:
            print(f"\n📋 完整字段信息:")
            for key, value in row_all.items():
                print(f"  {key}: {value}")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_specific_document())
