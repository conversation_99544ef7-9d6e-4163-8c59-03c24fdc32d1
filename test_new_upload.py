#!/usr/bin/env python3
import requests
import json
import time

def test_new_upload():
    """测试新文档上传"""
    url = "http://localhost:8000/api/v1/documents/upload"
    
    # 创建一个新的测试文档内容
    content = f"""这是一个新的测试文档 - {time.time()}

这个文档包含了关于知识库管理的内容。

知识库是一个用于存储和管理知识的系统。它可以帮助用户快速找到所需的信息。

在现代企业中，知识库管理系统变得越来越重要。它们可以提高工作效率，减少重复工作。

人工智能技术的发展使得知识库系统更加智能化。通过自然语言处理和机器学习技术，系统可以更好地理解和组织知识。"""
    
    files = {
        'file': (f'new_test_{int(time.time())}.txt', content, 'text/plain')
    }
    
    data = {
        'kb_id': 'test-kb-new-001',  # 新的知识库ID
        'auto_process': 'true',
        'uploaded_by': 'new_test_user'
    }
    
    try:
        print("🚀 开始测试新文档上传...")
        response = requests.post(url, files=files, data=data, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 新文档上传成功！")
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and result.get('data'):
                doc_id = result['data'].get('doc_id')
                if doc_id:
                    print(f"📄 新文档ID: {doc_id}")
                    return doc_id
        else:
            print(f"❌ 新文档上传失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return None

def test_list_with_new_kb():
    """测试新知识库的文档列表"""
    url = "http://localhost:8000/api/v1/documents/list"
    
    params = {
        'kb_id': 'test-kb-new-001',
        'page': 1,
        'page_size': 10
    }
    
    try:
        print("\n📋 测试新知识库文档列表...")
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            print("✅ 新知识库文档列表获取成功！")
            result = response.json()
            
            if result.get('success') and result.get('data'):
                documents = result['data'].get('records', [])
                print(f"📊 新知识库中找到 {len(documents)} 个文档:")
                
                for doc in documents:
                    print(f"  - 文件名: {doc.get('original_filename')}")
                    print(f"    知识库ID: {doc.get('kb_id')}")
                    print(f"    文档ID: {doc.get('doc_id')}")
                    print()
            else:
                print("📝 新知识库暂无文档")
        else:
            print(f"❌ 新知识库文档列表获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    # 测试新文档上传
    doc_id = test_new_upload()
    
    # 等待文档处理
    if doc_id:
        print("\n⏳ 等待文档处理...")
        time.sleep(5)
        
        # 测试新知识库文档列表
        test_list_with_new_kb()
