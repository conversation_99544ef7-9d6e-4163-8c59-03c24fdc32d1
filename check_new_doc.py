#!/usr/bin/env python3
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

async def check_new_document():
    # 加载环境变量
    load_dotenv()
    
    # 数据库连接参数
    db_config = {
        'host': os.getenv('POSTGRES_HOST', '*************'),
        'port': int(os.getenv('POSTGRES_PORT', 5433)),
        'database': os.getenv('POSTGRES_DB', 'pgsql'),
        'user': os.getenv('POSTGRES_USER', 'root'),
        'password': os.getenv('POSTGRES_PASSWORD', 'root123')
    }
    
    try:
        # 连接数据库
        conn = await asyncpg.connect(**db_config)
        
        # 查询最新的文档
        doc_id = "doc_20250721_130227_f49d3236"
        query = """
        SELECT doc_id, original_filename, kb_id, upload_status, process_status, 
               created_at, uploaded_by, file_size
        FROM mixrag_documents 
        WHERE doc_id = $1
        """
        
        row = await conn.fetchrow(query, doc_id)
        
        if row:
            print(f"📊 最新文档详情:")
            print(f"文档ID: {row['doc_id']}")
            print(f"文件名: {row['original_filename']}")
            print(f"知识库ID: {row['kb_id']}")
            print(f"上传状态: {row['upload_status']}")
            print(f"处理状态: {row['process_status']}")
            print(f"创建时间: {row['created_at']}")
            print(f"上传者: {row['uploaded_by']}")
            print(f"文件大小: {row['file_size']} bytes")
            
            if row['kb_id'] == 'final-test-kb-123':
                print("✅ 数据库中的 kb_id 正确！")
            else:
                print(f"❌ 数据库中的 kb_id 错误: {row['kb_id']}")
        else:
            print(f"❌ 未找到文档: {doc_id}")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

if __name__ == "__main__":
    asyncio.run(check_new_document())
