import React, { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { Layout, Menu } from 'antd'
import { 
  menuConfig, 
  defaultOpenKeys, 
  navigableKeys,
  DatabaseOutlined,
  RobotOutlined,
  DeploymentUnitOutlined,
  SettingOutlined,
  ApiOutlined,
  ExperimentOutlined
} from '../../constants/menuConfig'

const { Sider } = Layout

// 图标映射
const iconMap = {
  DatabaseOutlined,
  RobotOutlined,
  DeploymentUnitOutlined,
  SettingOutlined,
  ApiOutlined,
  ExperimentOutlined
}

const Sidebar = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [selectedKeys, setSelectedKeys] = useState([])
  const [openKeys, setOpenKeys] = useState(defaultOpenKeys)

  // 根据当前路由设置选中的菜单项
  useEffect(() => {
    const currentPath = location.pathname;
    const selected = navigableKeys.find(key => currentPath.startsWith(key));
    setSelectedKeys(selected ? [selected] : []);
  }, [location]);

  const handleMenuClick = ({ key }) => {
    // 只有叶子节点才能导航
    if (navigableKeys.includes(key)) {
      navigate(key)
    }
  }

  const handleOpenChange = (keys) => {
    setOpenKeys(keys)
  }

  // 将配置转换为antd需要的格式，动态创建图标
  const menuItems = menuConfig.map(item => {
    const IconComponent = item.icon ? iconMap[item.icon] : null
    return {
      ...item,
      icon: IconComponent ? React.createElement(IconComponent) : null
    }
  })

  return (
    <Sider width={250} theme="light">
      <div style={{ 
        padding: '16px', 
        borderBottom: '1px solid #f0f0f0',
        fontWeight: 'bold',
        fontSize: '16px',
        textAlign: 'center'
      }}>
        MIXRAG系统
      </div>
      <Menu
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={openKeys}
        onOpenChange={handleOpenChange}
        onClick={handleMenuClick}
        items={menuItems}
        style={{ borderRight: 0, height: 'calc(100vh - 64px)' }}
      />
    </Sider>
  )
}

export default Sidebar 