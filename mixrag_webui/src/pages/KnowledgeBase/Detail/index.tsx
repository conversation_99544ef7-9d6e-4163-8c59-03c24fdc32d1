import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Tabs, Card, Typography, Spin, message, Breadcrumb } from 'antd';
import { DatabaseOutlined, FileTextOutlined, NodeIndexOutlined, ShareAltOutlined } from '@ant-design/icons';
import PageWrapper from '../../../components/common/PageWrapper';
import DocumentPage from '../../Document';
import ChunkPage from '../../Chunk';
import GraphPage from '../../Graph';
import { fetchKnowledgeBaseDetail } from '../../../api/knowledgeBaseApi';



const KnowledgeBaseDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const [knowledgeBase, setKnowledgeBase] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadKnowledgeBaseDetail = async () => {
      if (!id) {
        console.error('知识库ID未定义');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetchKnowledgeBaseDetail(id);
        if (response.success) {
          setKnowledgeBase(response.data);
        } else {
          message.error(response.message || '获取知识库详情失败');
        }
      } catch (error: any) {
        console.error('获取知识库详情失败:', error);
        message.error(error.message || '获取知识库详情失败');
      } finally {
        setLoading(false);
      }
    };

    loadKnowledgeBaseDetail();
  }, [id]);

  const tabItems = React.useMemo(() => [
    {
      key: 'document',
      label: (
        <span>
          <FileTextOutlined />
          Document
        </span>
      ),
      children: id ? <DocumentPage kb_id={id} /> : <div>知识库ID未定义</div>
    },
    {
      key: 'chunk-search',
      label: (
        <span>
          <NodeIndexOutlined />
          Chunk搜索
        </span>
      ),
      children: id ? <ChunkPage kb_id={id} activeTab="chunk-search" /> : <div>知识库ID未定义</div>
    },
    {
      key: 'chunk-list',
      label: (
        <span>
          <NodeIndexOutlined />
          Chunk列表
        </span>
      ),
      children: id ? <ChunkPage kb_id={id} activeTab="chunk-list" /> : <div>知识库ID未定义</div>
    },
    {
      key: 'graph-statistics',
      label: (
        <span>
          <ShareAltOutlined />
          图统计
        </span>
      ),
      children: id ? <GraphPage kb_id={id} activeTab="graph-statistics" /> : <div>知识库ID未定义</div>
    },
    {
      key: 'graph-visualization',
      label: (
        <span>
          <ShareAltOutlined />
          图可视化
        </span>
      ),
      children: id ? <GraphPage kb_id={id} activeTab="graph-visualization" /> : <div>知识库ID未定义</div>
    },
    {
      key: 'graph-nodes',
      label: (
        <span>
          <ShareAltOutlined />
          节点
        </span>
      ),
      children: id ? <GraphPage kb_id={id} activeTab="graph-nodes" /> : <div>知识库ID未定义</div>
    },
    {
      key: 'graph-edges',
      label: (
        <span>
          <ShareAltOutlined />
          边
        </span>
      ),
      children: id ? <GraphPage kb_id={id} activeTab="graph-edges" /> : <div>知识库ID未定义</div>
    }
  ], [id]);

  if (loading) {
    return (
      <PageWrapper>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <Card>
        <Breadcrumb style={{ marginBottom: '24px' }}>
          <Breadcrumb.Item>
            <Link to="/knowledge-base">知识库</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{knowledgeBase?.kb_name || '详情'}</Breadcrumb.Item>
        </Breadcrumb>

        {/* Tab内容 */}
        <Tabs defaultActiveKey="document" items={tabItems} type="card" size="large" />
      </Card>
    </PageWrapper>
  );
};

export default KnowledgeBaseDetailPage;
