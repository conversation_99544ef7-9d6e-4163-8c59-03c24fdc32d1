import React, { useState, useEffect } from 'react'
import { 
  Card, 
  Button, 
  Space, 
  Row, 
  Col,
  Pagination,
  Statistic,
  Divider
} from 'antd'
import { 
  UploadOutlined,
  ReloadOutlined,
  BarChartOutlined
} from '@ant-design/icons'
import PageWrapper from '../../components/common/PageWrapper'
import DocumentTable from '../../components/Document/DocumentTable'
import DocumentUpload from '../../components/Document/DocumentUpload'
import DocumentFilter from '../../components/Document/DocumentFilter'
import DocumentDetailModal from '../../components/Document/DocumentDetailModal'
import { useDocumentManagement } from '../../hooks/useDocumentManagement'

interface DocumentManagementProps {
  kb_id?: string
}

const DocumentManagement: React.FC<DocumentManagementProps> = ({ kb_id }) => {
  const [isUploadVisible, setIsUploadVisible] = useState(false)
  const [isDetailVisible, setIsDetailVisible] = useState(false)

  const {
    // 数据状态
    mixrag_documents,
    loading,
    uploadLoading,
    total,
    currentPage,
    pageSize,
    uploadStatusFilter,
    processStatusFilter,
    selectedDocument,
    stats,
    
    // 操作方法
    loadDocuments,
    loadDocumentDetail,
    handleUploadDocument,
    handleDeleteDocument,
    handleProcessDocument,
    handleDownloadDocument,
    loadStats,
    
    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setUploadStatusFilter,
    setProcessStatusFilter,
    setSelectedDocument
  } = useDocumentManagement(kb_id)

  // 组件挂载时加载统计信息
  useEffect(() => {
    loadStats()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 处理查看文档详情
  const handleViewDocument = async (docId) => {
    try {
      await loadDocumentDetail(docId)
      setIsDetailVisible(true)
    } catch {
      // 错误已在Hook中处理
    }
  }

  // 处理文档上传
  const handleUpload = async (file, uploadedBy) => {
    try {
      await handleUploadDocument(file, uploadedBy)
      setIsUploadVisible(false)
      // 重新加载统计信息
      loadStats()
    } catch {
      // 错误已在Hook中处理
    }
  }

  // 处理文档删除
  const handleDelete = async (docId) => {
    try {
      await handleDeleteDocument(docId)
      // 重新加载统计信息
      loadStats()
    } catch {
      // 错误已在Hook中处理
    }
  }

  // 处理文档处理
  const handleProcess = async (docId, forceReprocess = false) => {
    try {
      await handleProcessDocument(docId, forceReprocess)
      // 重新加载统计信息
      loadStats()
    } catch {
      // 错误已在Hook中处理
    }
  }

  // 处理分页变化
  const handlePageChange = (page, size) => {
    setCurrentPage(page)
    setPageSize(size)
  }

  // 处理筛选变化
  const handleFilterChange = (uploadStatus, processStatus) => {
    setUploadStatusFilter(uploadStatus)
    setProcessStatusFilter(processStatus)
    setCurrentPage(1) // 筛选时重置到第一页
  }

  return (
    <PageWrapper>
      

      {/* 主要内容 */}
      <Card>
        {/* 操作栏 */}
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Button 
                type="primary" 
                icon={<UploadOutlined />}
                onClick={() => setIsUploadVisible(true)}
              >
                上传文档
              </Button>
              <Button 
                icon={<ReloadOutlined />}
                onClick={() => {
                  loadDocuments()
                  loadStats()
                }}
              >
                刷新
              </Button>
            </Space>
          </Col>
          <Col>
            <DocumentFilter
              uploadStatusFilter={uploadStatusFilter}
              processStatusFilter={processStatusFilter}
              onFilterChange={handleFilterChange}
            />
          </Col>
        </Row>

        <Divider />

        {/* 文档列表表格 */}
        <DocumentTable
          mixrag_documents={mixrag_documents}
          loading={loading}
          onViewDocument={handleViewDocument}
          onDeleteDocument={handleDelete}
          onProcessDocument={handleProcess}
          onDownloadDocument={handleDownloadDocument}
        />

        {/* 分页 */}
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
            }
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
            pageSizeOptions={['10', '20', '50', '100']}
          />
        </div>
      </Card>

      {/* 上传文档弹窗 */}
      <DocumentUpload
        visible={isUploadVisible}
        onCancel={() => setIsUploadVisible(false)}
        onUpload={handleUpload}
        loading={uploadLoading}
      />

      {/* 文档详情弹窗 */}
      <DocumentDetailModal
        visible={isDetailVisible}
        onCancel={() => {
          setIsDetailVisible(false)
          setSelectedDocument(null)
        }}
        document={selectedDocument}
        onProcessDocument={handleProcess}
      />
    </PageWrapper>
  )
}

export default DocumentManagement 