import { useState } from 'react'
import { message } from 'antd'
import {
  getChunkList,
  getChunkStats,
  getChunksByDocument,
  searchChunksBySimilarity,
  searchChunksByKeywords
} from '../api/chunkApi'

export const useChunkManagement = (kb_id?: string) => {
  // 基础状态
  const [chunks, setChunks] = useState([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(20)
  const [stats, setStats] = useState(null)
  
  // 搜索状态
  const [searchResults, setSearchResults] = useState([])
  const [searchLoading, setSearchLoading] = useState(false)
  const [searchTotal, setSearchTotal] = useState(0)
  
  // 过滤状态
  const [selectedDocId, setSelectedDocId] = useState(null)

  // 加载chunk列表
  const loadChunks = async (params = {}) => {
    try {
      setLoading(true)
      const requestParams = {
        page: currentPage,
        page_size: pageSize,
        ...params
      }
      
      // 只有当kb_id有值时才添加
      if (kb_id) {
        requestParams.kb_id = kb_id
      }

      // 只有当selectedDocId有值时才添加doc_id参数
      if (selectedDocId) {
        requestParams.doc_id = selectedDocId
      }

      // 发送请求参数
      const response = await getChunkList(requestParams)
      
      // 适配统一响应格式
      if (response.success && response.data) {
        setChunks(response.data.records || [])
        setTotal(response.data.pagination?.total || 0)
      } else {
        setChunks([])
        setTotal(0)
        console.error('获取chunks失败:', response.message)
      }
    } catch (error) {
      message.error(`加载chunk列表失败: ${error.message}`)
      setChunks([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }

  // 加载统计信息
  const loadStats = async () => {
    try {
      const response = await getChunkStats({ kb_id })
      setStats(response)
    } catch (error) {
      message.error(`加载统计信息失败: ${error.message}`)
      setStats(null)
    }
  }

  // 按文档ID加载chunks
  const loadChunksByDocument = async (docId, params = {}) => {
    try {
      setLoading(true)
      const requestParams = {
        page: currentPage,
        page_size: pageSize,
        ...params
      }
      
      const response = await getChunksByDocument(docId, requestParams)
      
      // 适配统一响应格式
      if (response.success && response.data) {
        setChunks(response.data.records || [])
        setTotal(response.data.pagination?.total || 0)
      } else {
        setChunks([])
        setTotal(0)
        console.error('获取文档chunks失败:', response.message)
      }
    } catch (error) {
      message.error(`加载文档chunks失败: ${error.message}`)
      setChunks([])
      setTotal(0)
    } finally {
      setLoading(false)
    }
  }

  // 相似度搜索
  const searchBySimilarity = async (query, options = {}) => {
    try {
      setSearchLoading(true)
      const requestData = {
        query,
        top_k: 10,
        threshold: 0.2,
        ...options
      }
      
      // 只有当kb_id有值时才添加
      if (kb_id) {
        requestData.kb_id = kb_id
      }

      // 只有当selectedDocId有值时才添加doc_id参数
      if (selectedDocId) {
        requestData.doc_id = selectedDocId
      }
      
      const response = await searchChunksBySimilarity(requestData)

      console.log('相似度搜索响应:', response)
      console.log('搜索结果数据:', response.data?.chunks)

      setSearchResults(response.data?.chunks || [])
      setSearchTotal(response.data?.total || 0)
      
      return response
    } catch (error) {
      message.error(`相似度搜索失败: ${error.message}`)
      setSearchResults([])
      setSearchTotal(0)
      throw error
    } finally {
      setSearchLoading(false)
    }
  }

  // 关键词搜索
  const searchByKeywords = async (keywords, options = {}) => {
    try {
      setSearchLoading(true)
      const requestData = {
        keywords,
        limit: 10,
        offset: 0,
        ...options
      }
      
      // 只有当kb_id有值时才添加
      if (kb_id) {
        requestData.kb_id = kb_id
      }

      // 只有当selectedDocId有值时才添加doc_id参数
      if (selectedDocId) {
        requestData.doc_id = selectedDocId
      }
      
      const response = await searchChunksByKeywords(requestData)

      setSearchResults(response.data?.chunks || [])
      setSearchTotal(response.data?.total || 0)
      
      return response
    } catch (error) {
      message.error(`关键词搜索失败: ${error.message}`)
      setSearchResults([])
      setSearchTotal(0)
      throw error
    } finally {
      setSearchLoading(false)
    }
  }

  // 清空搜索结果
  const clearSearchResults = () => {
    setSearchResults([])
    setSearchTotal(0)
  }

  return {
    // 数据状态
    chunks,
    loading,
    total,
    currentPage,
    pageSize,
    stats,
    
    // 搜索状态
    searchResults,
    searchLoading,
    searchTotal,
    
    // 过滤状态
    selectedDocId,
    
    // 操作方法
    loadChunks,
    loadStats,
    loadChunksByDocument,
    searchBySimilarity,
    searchByKeywords,
    clearSearchResults,
    
    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setSelectedDocId,
    setChunks,
    setTotal
  }
} 