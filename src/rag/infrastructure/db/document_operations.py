"""
文档数据库操作

提供文档相关数据的 CRUD 操作
"""

from datetime import datetime, timezone
from typing import Any, Dict, Optional

from .client import postgre_sql_client
from src.rag.tools.logger import logger


class DocumentOperations:
    """文档数据库操作类"""

    async def list_documents(
            self,
            page: int = 1,
            page_size: int = 20,
            search_query: str = None,
            filters: Dict[str, Any] = None,
            sort_by: str = "created_at",
            sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """列出文档"""
        offset = (page - 1) * page_size

        # 构建查询条件
        where_conditions = ["1=1"]
        params = []
        param_count = 0

        # 处理搜索查询（保持向后兼容）
        if search_query:
            param_count += 1
            where_conditions.append(f"(original_filename ILIKE ${param_count})")
            params.append(f"%{search_query}%")

        # 处理过滤条件
        if filters:
            for key, value in filters.items():
                if value is not None:
                    param_count += 1
                    if key == 'filename_like':
                        where_conditions.append(f"(original_filename ILIKE ${param_count})")
                        params.append(value)
                    elif key == 'upload_status':
                        where_conditions.append(f"upload_status = ${param_count}")
                        params.append(value)
                    elif key == 'process_status':
                        where_conditions.append(f"process_status = ${param_count}")
                        params.append(value)

        where_clause = " AND ".join(where_conditions)

        # 构建排序子句
        valid_sort_fields = {
            'created_at', 'updated_at', 'original_filename', 'file_size',
            'upload_status', 'process_status'
        }
        if sort_by not in valid_sort_fields:
            sort_by = 'created_at'

        sort_order = sort_order.upper()
        if sort_order not in ['ASC', 'DESC']:
            sort_order = 'DESC'

        order_clause = f"ORDER BY {sort_by} {sort_order}"

        async with postgre_sql_client.pool.acquire() as conn:
            # 获取总数
            count_query = f"SELECT COUNT(*) FROM mixrag_documents WHERE {where_clause}"
            total = await conn.fetchval(count_query, *params)

            # 获取文档列表
            list_query = f"""
            SELECT doc_id, original_filename, file_extension, file_size, mime_type,
                   created_at, updated_at, upload_status, process_status, uploaded_by,
                   chunks_count, entities_count, relationships_count, minio_bucket, minio_object_key,
                   content_hash, error_message
            FROM mixrag_documents
            WHERE {where_clause}
            {order_clause}
            LIMIT ${param_count + 1} OFFSET ${param_count + 2}
            """
            params.extend([page_size, offset])

            rows = await conn.fetch(list_query, *params)

            documents = []
            for row in rows:
                doc = {
                    "doc_id": row["doc_id"],
                    "title": row["original_filename"],  # 使用原始文件名作为标题
                    "file_name": row["original_filename"],
                    "original_filename": row["original_filename"],
                    "file_extension": row["file_extension"],
                    "file_size": row["file_size"],
                    "file_type": row["mime_type"],
                    "mime_type": row["mime_type"],
                    "upload_time": row["created_at"].isoformat() if row["created_at"] else None,
                    "created_at": row["created_at"].isoformat() if row["created_at"] else None,
                    "updated_at": row["updated_at"].isoformat() if row["updated_at"] else None,
                    "upload_status": row["upload_status"],
                    "process_status": row["process_status"],
                    "uploaded_by": row["uploaded_by"],
                    "chunks_count": row["chunks_count"] or 0,
                    "entities_count": row["entities_count"] or 0,
                    "relationships_count": row["relationships_count"] or 0,
                    "minio_bucket": row["minio_bucket"],
                    "minio_object_key": row["minio_object_key"],
                    "content_hash": row["content_hash"],
                    "error_message": row["error_message"],
                }
                documents.append(doc)

            return {
                "documents": documents,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }

    async def get_document_stats(self) -> Dict[str, Any]:
        """获取文档统计信息"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 文档统计
            doc_stats = await conn.fetchrow("""
                                            SELECT COUNT(*)                                                  as total_documents,
                                                   COUNT(CASE WHEN process_status = 'completed' THEN 1 END)  as completed_documents,
                                                   COUNT(CASE WHEN process_status = 'processing' THEN 1 END) as processing_documents,
                                                   COUNT(CASE WHEN process_status = 'failed' THEN 1 END)     as failed_documents,
                                                   COALESCE(SUM(file_size), 0)                               as total_size,
                                                   COALESCE(AVG(file_size), 0)                               as avg_file_size
                                            FROM mixrag_documents
                                            """, )

            # 分块统计（从文档表中汇总）
            chunk_stats = await conn.fetchrow("""
                                              SELECT COALESCE(SUM(chunks_count), 0) as total_chunks
                                              FROM mixrag_documents
                                              """)

            # 实体统计（从文档表中汇总）
            entity_stats = await conn.fetchrow("""
                                               SELECT COALESCE(SUM(entities_count), 0) as total_entities
                                               FROM mixrag_documents
                                               """)

            return {
                "total_documents": doc_stats["total_documents"] or 0,
                "completed_documents": doc_stats["completed_documents"] or 0,
                "processing_count": doc_stats["processing_documents"] or 0,
                "failed_documents": doc_stats["failed_documents"] or 0,
                "storage_size_bytes": doc_stats["total_size"] or 0,
                "avg_file_size_bytes": doc_stats["avg_file_size"] or 0,
                "total_chunks": chunk_stats["total_chunks"] or 0 if chunk_stats else 0,
                "total_entities": entity_stats["total_entities"] or 0 if entity_stats else 0,
                "processing_queue_size": 0,  # 默认值
            }

    async def get_document_metadata(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """获取文档元数据"""
        async with postgre_sql_client.pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM mixrag_documents WHERE doc_id = $1",
                doc_id
            )

            if row:
                return dict(row)
            return None

    async def save_document_metadata(self, doc_metadata: Dict[str, Any]) -> bool:
        """保存文档元数据"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 插入文档记录
            await conn.execute("""
                               INSERT INTO mixrag_documents (doc_id, original_filename, file_extension, file_size, mime_type,
                                                             created_at, updated_at, upload_status, process_status,
                                                             uploaded_by, chunks_count, entities_count, relationships_count,
                                                             minio_bucket, minio_object_key, content_hash, error_message, kb_id)
                               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
                               """,
                               doc_metadata['doc_id'],
                               doc_metadata['original_filename'],
                               doc_metadata.get('file_extension'),
                               doc_metadata['file_size'],
                               doc_metadata['mime_type'],
                               doc_metadata['created_at'],
                               doc_metadata['updated_at'],
                               doc_metadata['upload_status'],
                               doc_metadata['process_status'],
                               doc_metadata.get('uploaded_by'),
                               doc_metadata.get('chunks_count', 0),
                               doc_metadata.get('entities_count', 0),
                               doc_metadata.get('relationships_count', 0),
                               doc_metadata['minio_bucket'],
                               doc_metadata['minio_object_key'],
                               doc_metadata.get('content_hash'),
                               doc_metadata.get('error_message'),
                               doc_metadata['kb_id']
                               )

            logger.info(f"文档元数据保存成功: {doc_metadata['doc_id']}")
            return True

    async def delete_document(self, doc_id: str) -> bool:
        """删除文档"""
        async with postgre_sql_client.pool.acquire() as conn:
            # 删除文档记录
            result = await conn.execute(
                "DELETE FROM mixrag_documents WHERE doc_id = $1",
                doc_id
            )

            # 检查是否删除了记录
            if result == "DELETE 0":
                logger.warning(f"文档 {doc_id} 不存在")
                return False

            logger.info(f"文档 {doc_id} 删除成功")
            return True

    async def update_document_metadata(self, doc_id: str, updates: Dict[str, Any]) -> bool:
        """更新文档元数据"""
        if not updates:
            return True
        # 构建更新语句
        set_clauses = []
        values = []
        param_index = 1

        for key, value in updates.items():
            if key != 'doc_id':  # 不允许更新主键
                set_clauses.append(f"{key} = ${param_index}")
                values.append(value)
                param_index += 1

        if not set_clauses:
            return True

        query = f"""
            UPDATE mixrag_documents
            SET {', '.join(set_clauses)}
            WHERE doc_id = ${param_index}
        """
        values.append(doc_id)

        async with postgre_sql_client.pool.acquire() as conn:
            result = await conn.execute(query, *values)
            return result == "UPDATE 1"

    async def get_document_by_hash(self, content_hash: str) -> Optional[Dict[str, Any]]:
        """根据内容哈希获取文档"""
        async with postgre_sql_client.pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM mixrag_documents WHERE content_hash = $1",
                content_hash
            )

            if row:
                return dict(row)
            return None

    async def get_document_by_hash_and_kb(self, content_hash: str, kb_id: str) -> Optional[Dict[str, Any]]:
        """根据内容哈希和知识库ID获取文档"""
        async with postgre_sql_client.pool.acquire() as conn:
            row = await conn.fetchrow(
                "SELECT * FROM mixrag_documents WHERE content_hash = $1 AND kb_id = $2",
                content_hash, kb_id
            )

            if row:
                return dict(row)
            return None

    async def update_document_status(
            self,
            doc_id: str,
            upload_status: str = None,
            process_status: str = None,
            task_id: str = None,
            error_message: str = None,
            chunks_count: int = None,
            entities_count: int = None,
            relationships_count: int = None
    ) -> bool:
        """
        更新文档状态

        Args:
            doc_id: 文档ID
            upload_status: 上传状态
            process_status: 处理状态
            task_id: 任务ID
            error_message: 错误信息
            chunks_count: 分块数量
            entities_count: 实体数量
            relationships_count: 关系数量

        Returns:
            bool: 更新是否成功
        """
        # 构建更新字段
        updates = {}
        if upload_status is not None:
            updates['upload_status'] = upload_status
        if process_status is not None:
            updates['process_status'] = process_status
        if task_id is not None:
            updates['pipeline_id'] = task_id
        if error_message is not None:
            updates['error_message'] = error_message
        if chunks_count is not None:
            updates['chunks_count'] = chunks_count
        if entities_count is not None:
            updates['entities_count'] = entities_count
        if relationships_count is not None:
            updates['relationships_count'] = relationships_count

        if not updates:
            return True

        # 添加更新时间
        updates['updated_at'] = datetime.now(timezone.utc).replace(tzinfo=None)

        # 使用现有的 update_document_metadata 方法
        return await self.update_document_metadata(doc_id, updates)


# 全局文档操作实例
document_operations = DocumentOperations()