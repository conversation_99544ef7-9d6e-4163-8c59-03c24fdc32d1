"""
文档处理路由模块 - 接口层

📤 职责：
- 参数接收和验证
- 调用业务层服务
- 返回标准化响应

三层架构：Route → Service → RAG
"""

from typing import Optional, List

from fastapi import APIRouter, UploadFile, File, Form, Query

from src.api.schemas.request import DocumentPipelineRequest
from src.api.schemas.response import CreatePipelineResponse
from src.api.schemas.response import (
    DocumentUploadResponse,
    DocumentListResponse,
    DocumentDeleteResponse,
    DocumentStatsResponse
)
from src.api.service.document_service import document_service

# 创建路由器
router = APIRouter()



@router.post("/pipeline", response_model=CreatePipelineResponse)
async def create_document_pipeline(
        request: DocumentPipelineRequest,

):
    """创建文档处理流水线 - 接口层"""
    return await document_service.create_document_pipeline(request)


@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_single_file(
    file: UploadFile = File(...),
    kb_id: str = Form(...),
    auto_process: bool = Form(True),
    uploaded_by: Optional[str] = Form(None),

):
    """上传单个文件 - 接口层"""
    return await document_service.upload_single_file(
        file=file,
        kb_id=kb_id,
        auto_process=auto_process,
        uploaded_by=uploaded_by
    )


@router.post("/upload/batch", response_model=List[DocumentUploadResponse])
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    kb_id: str = Form(...),
    auto_process: bool = Form(True),

):
    """批量上传文件 - 接口层"""
    return await document_service.upload_multiple_files(
        files=files,
        kb_id=kb_id,
        auto_process=auto_process
    )


@router.get("/list", response_model=DocumentListResponse)
async def list_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    upload_status: Optional[str] = Query(None, description="上传状态"),
    process_status: Optional[str] = Query(None, description="处理状态"),
    filename_filter: Optional[str] = Query(None, description="文件名过滤"),
    kb_id: Optional[str] = Query(None, description="知识库ID"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", description="排序顺序"),

):
    """获取文档列表 - 接口层"""
    return await document_service.list_documents(
        page=page,
        page_size=page_size,
        upload_status=upload_status,
        process_status=process_status,
        filename_filter=filename_filter,
        kb_id=kb_id,
        sort_by=sort_by,
        sort_order=sort_order
    )


@router.get("/stats", response_model=DocumentStatsResponse)
async def get_document_stats(

):
    """获取文档统计信息 - 接口层"""
    return await document_service.get_document_stats()


@router.get("/search", response_model=DocumentListResponse)
async def search_documents(
        query: str = Query(..., description="搜索查询"),
        page: int = Query(1, ge=1, description="页码"),
        page_size: int = Query(20, ge=1, le=100, description="每页数量"),

):
    """搜索文档 - 接口层"""
    return await document_service.search_documents(
        query=query,
        page=page,
        page_size=page_size
    )


@router.get("/{doc_id}")
async def get_document_info(
    doc_id: str,

):
    """获取文档信息 - 接口层"""
    return await document_service.get_document_info(doc_id=doc_id)


@router.get("/{doc_id}/stats")
async def get_document_processing_stats(
        doc_id: str,

):
    """获取文档处理统计信息 - 接口层"""
    return await document_service.get_document_processing_stats(doc_id=doc_id)


@router.get("/{doc_id}/download")
async def download_document(
    doc_id: str,

):
    """下载文档 - 接口层"""
    return await document_service.download_document(doc_id=doc_id)


@router.get("/{doc_id}/content")
async def get_document_content(
    doc_id: str,

):
    """获取文档内容 - 接口层"""
    return await document_service.get_document_content(doc_id=doc_id)


@router.delete("/{doc_id}", response_model=DocumentDeleteResponse)
async def delete_document(
    doc_id: str,

):
    """删除文档 - 接口层"""
    result = await document_service.delete_document(doc_id=doc_id)

    # 从删除详情中提取各系统的删除状态
    deletion_details = result.get("deletion_details", {})

    return DocumentDeleteResponse(
        doc_id=result["doc_id"],
        overall_success=result.get("overall_success", True),
        message=result["message"],
        postgresql_deleted=deletion_details.get("postgresql", {}).get("success", False),
        minio_deleted=deletion_details.get("minio", {}).get("success", False),
        neo4j_deleted=deletion_details.get("neo4j", {}).get("success", False),
        milvus_deleted=deletion_details.get("milvus", {}).get("success", False),
        redis_deleted=deletion_details.get("redis", {}).get("success", False),
        deleted_from=result.get("deleted_from", []),
        failed_systems=result.get("failed_systems", [])
    )
